# =============================================================================
# DIGITAL INVOICE & IPTV SUBSCRIPTION MANAGEMENT SYSTEM
# Environment Variables Configuration Template
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Database connection URL - supports MySQL, PostgreSQL, and SQLite
# Examples:
# MySQL: mysql://username:password@localhost:3306/database_name
# PostgreSQL: postgresql://username:password@localhost:5432/database_name
# SQLite: sqlite:./database.db
DATABASE_URL=mysql://user:password@localhost:3306/digital_invoice_db

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Application environment (development, production, test)
NODE_ENV=development

# Server port (default: 3001)
PORT=3001

# Session secret for secure session management (minimum 32 characters)
# Generate a strong secret: openssl rand -base64 32
SESSION_SECRET=your_very_secure_session_secret_here_minimum_32_characters

# Cookie security settings
SECURE_COOKIES=false

# Admin access protection token
ADMIN_ACCESS_TOKEN=your_secure_admin_access_token
VITE_ADMIN_ACCESS_TOKEN=your_secure_admin_access_token

# =============================================================================
# PAYPAL CONFIGURATION (Optional)
# =============================================================================
# PayPal API credentials for payment processing
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox
PAYPAL_EMAIL=<EMAIL>

# =============================================================================
# TELEGRAM BOT CONFIGURATION (Optional)
# =============================================================================
# Telegram bot for notifications and management
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/api/telegram/webhook

# =============================================================================
# EMAIL CONFIGURATION (Optional - can be configured via admin panel)
# =============================================================================
# Primary SMTP provider settings
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Your Business Name

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
# Maximum file size in bytes (default: 10MB)
MAX_FILE_SIZE=10485760

# Upload directory path
UPLOAD_PATH=./uploads

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Site branding and information
SITE_NAME=Your Business Name
SITE_DESCRIPTION=Your business description
SITE_URL=https://yourdomain.com

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Rate limiting settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# IP restriction settings (comma-separated list)
ALLOWED_IPS=

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Log file path
LOG_FILE=./logs/app.log

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Health check interval in minutes
HEALTH_CHECK_INTERVAL=5

# System monitoring settings
ENABLE_SYSTEM_MONITORING=true
MONITORING_INTERVAL_MINUTES=5

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Enable debug mode (development only)
DEBUG_MODE=false

# Enable hot reload (development only)
HOT_RELOAD=true

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Automatic backup settings
ENABLE_AUTO_BACKUP=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30

# =============================================================================
# NOTES
# =============================================================================
# 1. Copy this file to .env for development or .env.production for production
# 2. Replace all placeholder values with your actual configuration
# 3. Never commit .env files to version control
# 4. Use strong, unique passwords and secrets
# 5. For production, ensure SECURE_COOKIES=true and use HTTPS
# 6. Generate strong SESSION_SECRET: openssl rand -base64 32
# 7. Keep your API keys and tokens secure and rotate them regularly
