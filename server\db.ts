import { drizzle } from 'drizzle-orm/better-sqlite3';
import { drizzle as drizzleMysql } from 'drizzle-orm/mysql2';
import { drizzle as drizzlePostgres } from 'drizzle-orm/postgres-js';
import Database from 'better-sqlite3';
import mysql from 'mysql2/promise';
import postgres from 'postgres';
import * as dotenv from 'dotenv';
import * as schema from '../shared/schema';

// Load environment variables from appropriate .env file
if (process.env.NODE_ENV === 'production') {
  dotenv.config({ path: '.env.production', override: true });
} else {
  dotenv.config();
}

// Get DATABASE_URL from environment
const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required. Please create a .env file with DATABASE_URL.");
}

// Determine database type and create appropriate connection
let db: any;

if (DATABASE_URL.startsWith('sqlite:')) {
  console.log('🗄️ Connecting to SQLite database...');
  const dbPath = DATABASE_URL.replace('sqlite:', '');
  const sqlite = new Database(dbPath);
  db = drizzle(sqlite, { schema });
  console.log('✅ SQLite database connected successfully');
} else if (DATABASE_URL.startsWith('mysql:')) {
  console.log('🗄️ Connecting to MySQL database...');
  try {
    const connection = mysql.createPool({
      uri: DATABASE_URL,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      // Removed deprecated options that cause warnings:
      // acquireTimeout, timeout, reconnect
      // These are handled by the connection pool automatically
    });
    db = drizzleMysql(connection, { schema, mode: 'default' });
    console.log('✅ MySQL database connected successfully');
  } catch (error) {
    console.error('❌ Failed to connect to MySQL database:', error);
    throw error;
  }
} else if (DATABASE_URL.startsWith('postgres:') || DATABASE_URL.startsWith('postgresql:')) {
  console.log('🗄️ Connecting to PostgreSQL database...');
  try {
    const client = postgres(DATABASE_URL);
    db = drizzlePostgres(client, { schema });
    console.log('✅ PostgreSQL database connected successfully');
  } catch (error) {
    console.error('❌ Failed to connect to PostgreSQL database:', error);
    throw error;
  }
} else {
  throw new Error(`Unsupported database URL format: ${DATABASE_URL}. Supported formats: sqlite:, mysql:, postgres:, postgresql:`);
}

// Export the database connection
export { db };
export default db;
